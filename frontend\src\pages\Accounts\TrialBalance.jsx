import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FiPrinter } from 'react-icons/fi';

export const TrialBalance = () => {
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [trialBalanceData, setTrialBalanceData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showStatement, setShowStatement] = useState(false);
  const [showPrintButton, setShowPrintButton] = useState(false);

  // Set default dates on component mount
  useEffect(() => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const januaryFirst = `${currentYear}-01-01`;
    const today = currentDate.toISOString().split('T')[0];

    setFromDate(januaryFirst);
    setToDate(today);
  }, []);

  const handleGenerateReport = async () => {
    if (!fromDate || !toDate) {
      setError('Please select both from and to dates');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post('http://127.0.0.1:8000/api/trial-balance', {
        from_date: fromDate,
        to_date: toDate,
      });

      if (response.data.success) {
        setTrialBalanceData(response.data.data);
        setShowStatement(true);
        setShowPrintButton(true);
      } else {
        throw new Error(response.data.message || 'Failed to generate trial balance');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to generate trial balance. Please try again.');
      console.error('Error generating trial balance:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) {
      return "0.00";
    }
    return new Intl.NumberFormat("en-LK", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Math.abs(numericAmount));
  };

  return (
    <div className="min-h-screen p-6 bg-gray-50 dark:bg-gray-900 print:p-0 print:bg-white">
      <div className="mx-auto max-w-7xl print:max-w-none">
        <style jsx>{`
          @media print {
            body { margin: 0; }
            .print\\:hidden { display: none !important; }
            .print\\:block { display: block !important; }
            .print\\:p-0 { padding: 0 !important; }
            .print\\:bg-white { background-color: white !important; }
            .print\\:max-w-none { max-width: none !important; }
            .print\\:shadow-none { box-shadow: none !important; }
            .print\\:rounded-none { border-radius: 0 !important; }
            .print\\:mb-4 { margin-bottom: 1rem !important; }
          }
        `}</style>
        {/* Header */}
        <div className="p-6 mb-6 bg-white rounded-lg shadow-sm dark:bg-gray-800 print:hidden">
          <h1 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white">
            Trial Balance
          </h1>

          {/* Date Selection */}
          <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                From Date
              </label>
              <input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                To Date
              </label>
              <input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div className="flex items-end gap-2">
              <button
                onClick={handleGenerateReport}
                disabled={loading}
                className="flex-1 px-4 py-2 font-medium text-white transition-colors duration-200 bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-blue-400"
              >
                {loading ? 'Generating...' : 'Generate Report'}
              </button>
              {showPrintButton && (
                <button
                  onClick={handlePrint}
                  className="flex items-center gap-2 px-4 py-2 font-medium text-white transition-colors duration-200 bg-green-600 rounded-md hover:bg-green-700"
                >
                  <FiPrinter className="w-4 h-4" />
                  Print
                </button>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-4 mb-6 border border-red-200 rounded-md bg-red-50 dark:bg-red-900/20 dark:border-red-800">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
        </div>

        {/* Trial Balance Statement */}
        {showStatement && trialBalanceData && (
          <div className="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-800 print:shadow-none print:rounded-none">
            <div className="mb-6 text-center print:mb-4">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Trial Balance
              </h2>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                From {new Date(fromDate).toLocaleDateString()} to {new Date(toDate).toLocaleDateString()}
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full border border-collapse border-gray-300 dark:border-gray-600">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="px-4 py-3 font-semibold text-left text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      Account Name
                    </th>
                    <th className="px-4 py-3 font-semibold text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      Debit (Rs.)
                    </th>
                    <th className="px-4 py-3 font-semibold text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      Credit (Rs.)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {trialBalanceData.accounts.map((account, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-4 py-3 text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                        {account.name}
                      </td>
                      <td className="px-4 py-3 text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                        {account.debit > 0 ? formatCurrency(account.debit) : '-'}
                      </td>
                      <td className="px-4 py-3 text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                        {account.credit > 0 ? formatCurrency(account.credit) : '-'}
                      </td>
                    </tr>
                  ))}
                  {/* Total Row */}
                  <tr className="font-semibold bg-gray-100 dark:bg-gray-600">
                    <td className="px-4 py-3 text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      Total
                    </td>
                    <td className="px-4 py-3 text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      {formatCurrency(trialBalanceData.totals.total_debit)}
                    </td>
                    <td className="px-4 py-3 text-right text-gray-900 border border-gray-300 dark:border-gray-600 dark:text-white">
                      {formatCurrency(trialBalanceData.totals.total_credit)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Balance Check */}
            <div className="mt-4 text-center">
              <p className={`text-sm font-medium ${
                Math.abs(trialBalanceData.totals.total_debit - trialBalanceData.totals.total_credit) < 0.01
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {Math.abs(trialBalanceData.totals.total_debit - trialBalanceData.totals.total_credit) < 0.01
                  ? 'Trial Balance is balanced ✓'
                  : `Difference: Rs. ${formatCurrency(Math.abs(trialBalanceData.totals.total_debit - trialBalanceData.totals.total_credit))}`
                }
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
